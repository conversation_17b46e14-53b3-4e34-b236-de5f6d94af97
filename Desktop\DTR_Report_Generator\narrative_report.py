from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.shared import Pt

def create_narrative_report():
    # Create a new document
    doc = Document()
    
    # Configure styles
    styles = doc.styles
    
    # Title page
    title = doc.add_heading('NARRATIVE REPORT', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_paragraph()
    doc.add_paragraph()
    
    subtitle = doc.add_paragraph('On-the-Job Training Experience')
    subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
    subtitle_run = subtitle.runs[0]
    subtitle_run.font.size = Pt(14)
    subtitle_run.bold = True
    
    doc.add_paragraph()
    
    company_info = doc.add_paragraph('EdnSoftTech Web Solutions Company')
    company_info.alignment = WD_ALIGN_PARAGRAPH.CENTER
    company_run = company_info.runs[0]
    company_run.font.size = Pt(12)
    
    doc.add_paragraph()
    doc.add_paragraph()
    
    prepared_by = doc.add_paragraph('Prepared by:')
    prepared_by.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    name = doc.add_paragraph('Jordan M. Bunuan')
    name.alignment = WD_ALIGN_PARAGRAPH.CENTER
    name_run = name.runs[0]
    name_run.bold = True
    
    course = doc.add_paragraph('Bachelor of Science in Computer Science')
    course.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    date = doc.add_paragraph('July 2025')
    date.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Page break
    doc.add_page_break()
    
    # Section 1: Background of the business
    doc.add_heading('1. BACKGROUND OF THE BUSINESS', level=1)
    
    background_text = doc.add_paragraph()
    background_text.add_run(
        "EdnSoftTech Web Solutions Company is a dynamic and innovative technology firm that has established "
        "itself as a leading provider of custom web development solutions. Founded with the vision to deliver "
        "cutting-edge software solutions to businesses of all sizes, the company has built a reputation for "
        "excellence in the digital transformation space.\n\n"
        
        "The company's journey began as a small startup focused on basic website development but has evolved "
        "into a comprehensive technology solutions provider. Over the years, EdnSoftTech has expanded its "
        "service offerings to include complex web applications, mobile development, and enterprise-level "
        "digital solutions. The company's growth trajectory reflects its commitment to staying current with "
        "emerging technologies and adapting to the ever-changing needs of the digital marketplace.\n\n"
        
        "EdnSoftTech operates with a client-centric approach, focusing on understanding unique business "
        "requirements and delivering tailored solutions that drive growth and efficiency. The company serves "
        "clients across various industries including healthcare, education, retail, finance, and manufacturing, "
        "demonstrating its versatility and expertise in diverse business domains.\n\n"
        
        "The company's core values center around innovation, quality, collaboration, and continuous learning. "
        "These values are reflected in every aspect of the business, from project delivery to employee "
        "development. EdnSoftTech maintains a strong commitment to professional development, making it an "
        "ideal environment for aspiring developers to gain practical experience and grow their careers."
    )
    
    # Section 2: Company Profile
    doc.add_heading('2. COMPANY PROFILE', level=1)
    
    profile_text = doc.add_paragraph()
    profile_text.add_run(
        "EdnSoftTech Web Solutions Company operates as a full-service technology partner, specializing in "
        "modern web development technologies and digital solutions. The company employs a team of highly "
        "skilled developers, designers, project managers, and technical specialists who work collaboratively "
        "to deliver exceptional results.\n\n"
        
        "The organizational structure promotes a flat hierarchy that encourages open communication, rapid "
        "decision-making, and collaborative problem-solving. This structure enables the company to remain "
        "agile and responsive to client needs while maintaining high standards of quality and innovation.\n\n"
        
        "The company's workforce consists of experienced professionals with expertise in various technologies "
        "including React.js, Node.js, WordPress, PHP, JavaScript, Python, and modern database management "
        "systems. The team's diverse skill set allows EdnSoftTech to handle projects of varying complexity "
        "and technical requirements.\n\n"
        
        "EdnSoftTech maintains strategic partnerships with leading technology providers and stays current with "
        "industry trends through continuous learning and professional development programs. The company's "
        "commitment to excellence is evidenced by its track record of successful project deliveries and "
        "long-term client relationships."
    )
    
    # Section 3: Products and Services
    doc.add_heading('3. DESCRIPTION OF PRODUCTS AND SERVICES', level=1)

    services_text = doc.add_paragraph()
    services_text.add_run(
        "EdnSoftTech Web Solutions Company offers a comprehensive range of technology services designed to "
        "meet the diverse needs of modern businesses. The company's service portfolio encompasses:\n\n"

        "Custom Web Application Development:\n"
        "The company specializes in developing bespoke web applications using modern frameworks such as "
        "React.js, Angular, and Vue.js for frontend development, coupled with robust backend solutions "
        "using Node.js, Express.js, and various database technologies. These applications are designed "
        "to be scalable, secure, and user-friendly.\n\n"

        "WordPress Development and Customization:\n"
        "EdnSoftTech provides comprehensive WordPress solutions including custom theme development, plugin "
        "creation and customization, e-commerce integration using WooCommerce, and ongoing maintenance "
        "services. The team has extensive experience in creating complex WordPress-based solutions for "
        "various business requirements.\n\n"

        "E-commerce Platform Development:\n"
        "The company develops robust e-commerce solutions with features including product catalog management, "
        "shopping cart functionality, payment gateway integration, inventory management, and customer "
        "relationship management systems. These platforms are optimized for performance and user experience.\n\n"

        "Mobile Application Development:\n"
        "EdnSoftTech offers mobile app development services for both iOS and Android platforms, utilizing "
        "modern development frameworks and ensuring cross-platform compatibility where appropriate.\n\n"

        "Database Design and Management:\n"
        "The company provides database architecture design, optimization, and management services using "
        "both SQL and NoSQL databases, ensuring data integrity, security, and optimal performance.\n\n"

        "Cloud Solutions and DevOps:\n"
        "EdnSoftTech assists clients with cloud migration, deployment automation, and DevOps implementation "
        "to improve development efficiency and system reliability.\n\n"

        "Digital Marketing and SEO Services:\n"
        "The company offers digital marketing solutions including search engine optimization, content "
        "management, and online presence enhancement to help clients achieve their business objectives.\n\n"

        "UI/UX Design and User Experience Optimization:\n"
        "EdnSoftTech provides comprehensive design services focusing on user experience, interface design, "
        "and usability testing to ensure optimal user engagement and satisfaction."
    )

    # Section 4: System Description
    doc.add_heading('4. SYSTEM DESCRIPTION AND TECHNICAL IMPLEMENTATION', level=1)

    system_text = doc.add_paragraph()
    system_text.add_run(
        "EdnSoftTech utilizes a modern, technology-driven approach to software development, employing "
        "industry-standard tools and methodologies to ensure high-quality deliverables.\n\n"

        "Development Technologies and Programming Languages:\n"
        "The company primarily uses JavaScript as the core programming language for both frontend and "
        "backend development. Frontend development is accomplished using React.js, HTML5, CSS3, and "
        "modern JavaScript (ES6+). Backend development utilizes Node.js with Express.js framework, "
        "while database management involves both SQL databases (MySQL, PostgreSQL) and NoSQL solutions "
        "(MongoDB). PHP is extensively used for WordPress development and legacy system maintenance.\n\n"

        "Development Workflow and System Flow:\n"
        "The development process follows agile methodologies with clearly defined phases:\n"
        "1. Requirements gathering and analysis\n"
        "2. System design and architecture planning\n"
        "3. Development and implementation\n"
        "4. Testing and quality assurance\n"
        "5. Deployment and production release\n"
        "6. Ongoing maintenance and support\n\n"

        "Version control is managed through Git and GitHub, enabling collaborative development and "
        "maintaining code history. The team uses project management tools like Asana for task tracking "
        "and team coordination.\n\n"

        "Problems Encountered and Solutions:\n"
        "During my internship, several technical challenges were encountered and successfully resolved:\n\n"

        "Payment Gateway Integration Issues:\n"
        "Problem: Complex payment processing errors and gateway compatibility issues in e-commerce "
        "applications.\n"
        "Solution: Implemented comprehensive error handling, created fallback payment methods, and "
        "developed robust testing procedures for payment processing workflows.\n\n"

        "Database Performance Optimization:\n"
        "Problem: Slow query performance and database bottlenecks affecting application responsiveness.\n"
        "Solution: Implemented database indexing, query optimization, pagination for large datasets, "
        "and caching mechanisms to improve overall system performance.\n\n"

        "Cross-Browser Compatibility:\n"
        "Problem: Inconsistent behavior across different web browsers and devices.\n"
        "Solution: Implemented progressive enhancement strategies, browser-specific polyfills, and "
        "comprehensive testing across multiple platforms and devices.\n\n"

        "User Interface and User Experience Issues:\n"
        "Problem: Complex UI components causing usability problems and poor user experience.\n"
        "Solution: Redesigned interface components with focus on accessibility, responsive design "
        "principles, and user-centered design methodologies."
    )

    # Section 5: Personal Observations
    doc.add_heading('5. PERSONAL OBSERVATIONS', level=1)

    # 5.1 Facilities
    doc.add_heading('5.1 Facilities', level=2)
    facilities_obs = doc.add_paragraph()
    facilities_obs.add_run(
        "The company operates in a modern web development office environment equipped with essential "
        "technological infrastructure. The workspace features contemporary workstations with high-performance "
        "computers suitable for web development tasks, reliable internet connectivity, and collaborative "
        "meeting spaces. The office maintains a professional atmosphere with adequate lighting, comfortable "
        "seating arrangements, and proper ventilation. The facility includes a pantry area with water "
        "dispensers and basic amenities to support daily operations. The workspace is designed to facilitate "
        "both individual focused work and team collaboration, with open areas that encourage communication "
        "and knowledge sharing among team members. The technical infrastructure supports multiple development "
        "environments and provides the necessary tools for efficient software development."
    )

    # 5.2 Services
    doc.add_heading('5.2 Services', level=2)
    services_obs = doc.add_paragraph()
    services_obs.add_run(
        "The company specializes in comprehensive web development services, with a primary focus on "
        "WordPress-based solutions and custom web applications. Key services include website development "
        "and maintenance, plugin development and customization, e-commerce solutions using WooCommerce, "
        "payment gateway integration, user interface and user experience design, database management, "
        "and technical troubleshooting. The company also provides ongoing support and maintenance for "
        "existing web applications, including bug fixes, performance optimization, and feature enhancements. "
        "Additionally, they offer consultation services for web development projects and provide training "
        "and mentorship through their internship programs. The service delivery approach emphasizes quality, "
        "client satisfaction, and long-term partnership relationships."
    )

    # 5.3 Employee
    doc.add_heading('5.3 Employee', level=2)
    employee_obs = doc.add_paragraph()
    employee_obs.add_run(
        "The company maintains a dynamic team of skilled web developers, designers, and technical "
        "professionals who demonstrate exceptional competitiveness and collaborative spirit. Employees "
        "are highly knowledgeable in various web technologies including WordPress, PHP, JavaScript, "
        "CSS, and database management. What stands out most is the culture of continuous learning and "
        "knowledge sharing among team members. Colleagues actively share tips, best practices, and "
        "innovative solutions to overcome development challenges. The team exhibits strong problem-solving "
        "skills and maintains a supportive environment where everyone contributes to finding solutions "
        "for complex technical issues. Employees show dedication to professional growth and are always "
        "willing to assist each other in troubleshooting and implementing new features. The competitive "
        "nature of the team drives innovation while the collaborative spirit ensures knowledge transfer "
        "and mutual support."
    )

    # 5.4 Management
    doc.add_heading('5.4 Management', level=2)
    management_obs = doc.add_paragraph()
    management_obs.add_run(
        "The management structure promotes an open and collaborative work environment that encourages "
        "innovation and professional development. Leadership demonstrates strong technical expertise "
        "and provides clear guidance on project requirements and deliverables. Management maintains "
        "regular communication through meetings, progress reviews, and feedback sessions to ensure "
        "project alignment and quality standards. They foster a culture of continuous improvement "
        "by encouraging employees to share ideas and implement creative solutions. The management "
        "team is approachable and supportive, providing necessary resources and mentorship for both "
        "regular employees and interns. They maintain a balance between maintaining productivity "
        "standards and allowing flexibility for learning and experimentation with new technologies. "
        "The leadership style promotes autonomy while ensuring accountability and quality deliverables."
    )

    # 5.5 Organization
    doc.add_heading('5.5 Organization', level=2)
    organization_obs = doc.add_paragraph()
    organization_obs.add_run(
        "As an organization, the company operates with a flat hierarchical structure that promotes "
        "open communication and rapid decision-making. The workflow is well-organized with clear "
        "project management processes, including task tracking through platforms like Asana and "
        "version control using GitHub. The organization maintains high standards for code quality "
        "and follows best practices in web development. There is a strong emphasis on documentation, "
        "testing, and collaborative development practices. The company culture emphasizes continuous "
        "learning, with regular knowledge sharing sessions and opportunities for professional growth. "
        "The organization successfully balances client project delivery with internal development "
        "initiatives, creating an environment where both business objectives and employee development "
        "are prioritized equally. The organizational structure supports innovation while maintaining "
        "operational efficiency and client satisfaction."
    )

    # Section 6: Recommendations
    doc.add_heading('6. RECOMMENDATIONS FOR OJT PROGRAM IMPLEMENTATION', level=1)

    recommendations_text = doc.add_paragraph()
    recommendations_text.add_run(
        "Based on my comprehensive observation and hands-on experience during the OJT program, I provide "
        "the following recommendations to further enhance the implementation and effectiveness of the "
        "internship program:\n\n"

        "Structured Mentorship Program:\n"
        "I highly recommend establishing a more structured mentorship program that pairs each intern "
        "with a dedicated senior developer from the beginning of the internship. This would provide "
        "consistent guidance, regular feedback, and personalized learning paths tailored to individual "
        "intern needs and career goals. The current collaborative culture is excellent, but formal "
        "mentorship would enhance the learning experience significantly.\n\n"

        "Enhanced Knowledge Documentation:\n"
        "The company should consider creating comprehensive internal knowledge bases and documentation "
        "systems to preserve the valuable insights, solutions, and best practices developed by the team. "
        "This would benefit both current and future interns by providing accessible learning resources "
        "and reducing the time needed to onboard new team members.\n\n"

        "Regular Technical Workshops:\n"
        "Implementing regular code review sessions and technical workshops would further enhance the "
        "learning experience for both interns and junior developers. These sessions could cover emerging "
        "technologies, best practices, and complex problem-solving techniques, fostering continuous "
        "learning and skill development.\n\n"

        "Structured Learning Milestones:\n"
        "Establishing clear learning milestones and skill assessments throughout the internship period "
        "would help track progress and ensure consistent development across all participants. This would "
        "also provide interns with clear goals and expectations.\n\n"

        "Industry Exposure Enhancement:\n"
        "Organizing visits to other technology companies, attending industry conferences, or inviting "
        "guest speakers would provide broader perspective on career opportunities and industry trends.\n\n"

        "Certification and Professional Development:\n"
        "Providing opportunities for interns to pursue relevant industry certifications during the "
        "internship would add significant value to their professional development and enhance their "
        "marketability upon completion.\n\n"

        "Conclusion:\n"
        "The current OJT program at EdnSoftTech is highly effective in providing practical experience "
        "and professional development opportunities. The competitive yet supportive environment, combined "
        "with hands-on project experience, creates an ideal learning environment for aspiring developers. "
        "The recommendations above would further strengthen an already excellent program and continue to "
        "produce well-prepared professionals ready to contribute effectively to the web development industry. "
        "The company's commitment to intern development and the collaborative culture make it an exemplary "
        "model for OJT program implementation in the technology sector."
    )

    # Save the document
    doc.save('Narrative_Report.docx')
    print("Narrative Report document has been created successfully!")

if __name__ == "__main__":
    create_narrative_report()
