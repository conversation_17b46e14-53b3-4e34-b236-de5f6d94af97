#!/usr/bin/env python3
"""
OJT Experience Document Generator
Creates a comprehensive Word document about internship experience
"""

from docx import Document
from docx.shared import Pt, Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.shared import OxmlElement, qn
from datetime import datetime

def add_page_break(doc):
    """Add a page break to the document"""
    doc.add_page_break()

def create_ojt_experience_document():
    """Create comprehensive OJT experience Word document"""
    
    # Create document
    doc = Document()
    
    # Set document margins
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(1)
        section.bottom_margin = Inches(1)
        section.left_margin = Inches(1)
        section.right_margin = Inches(1)
    
    # TITLE PAGE
    title = doc.add_heading('MY ON-THE-JOB TRAINING EXPERIENCE', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER

    doc.add_paragraph()

    subtitle = doc.add_paragraph()
    subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
    subtitle.add_run('Full Stack Developer Internship at EdnSoftTech Web Solutions Company').bold = True

    doc.add_paragraph()

    student_info = doc.add_paragraph()
    student_info.alignment = WD_ALIGN_PARAGRAPH.CENTER
    student_info.add_run('By: Jordan M. Bunuan\n')
    student_info.add_run('Computer Science Student\n')
    student_info.add_run(f'{datetime.now().strftime("%B %Y")}')

    doc.add_paragraph()
    doc.add_paragraph()

    # MAIN NARRATIVE ESSAY
    narrative_essay = """
    My 300-hour On-the-Job Training experience as a Full Stack Developer intern at EdnSoftTech Web Solutions Company has been one of the most transformative and educational periods of my academic journey. This internship provided me with invaluable hands-on experience that bridged the gap between theoretical knowledge gained in the classroom and the practical skills required in the professional software development industry.

    When I first walked into the offices of EdnSoftTech Web Solutions Company, I was both excited and nervous about what lay ahead. The company, known for its innovative web development solutions and client-focused approach, welcomed me into a dynamic environment where learning and growth were not just encouraged but expected. From day one, I was impressed by the company's commitment to fostering intern development and their willingness to invest time and resources in helping students like me gain meaningful experience.

    EdnSoftTech specializes in custom web development, e-commerce platforms, mobile applications, and digital transformation solutions. The company serves clients across various industries including healthcare, education, retail, and finance. What struck me most about the company culture was the emphasis on continuous learning, collaboration, and innovation. The team consisted of experienced developers, creative designers, and skilled project managers who were always willing to share their knowledge and provide guidance.

    During my first week, I was introduced to the company's development environment and coding standards. I quickly realized that the technologies I would be working with - React.js, Node.js, Express.js, and various databases - were exactly what I needed to learn to become a competent full-stack developer. My supervisor took the time to explain the company's project workflow, version control practices using Git, and the importance of writing clean, maintainable code.

    The learning curve was steep but exciting. I started with simple tasks like fixing minor bugs and implementing small features, which helped me understand the existing codebase and development practices. As my confidence grew, I was gradually assigned more complex responsibilities. One of my first significant contributions was working on the frontend components of an e-commerce platform. This project taught me the intricacies of React.js, including component lifecycle, state management, and the importance of creating reusable components.

    Working on the e-commerce project was particularly challenging because it required me to think about user experience, performance optimization, and responsive design. I learned to implement features like product filtering, shopping cart functionality, and user authentication. The project also introduced me to working with APIs and understanding how frontend and backend systems communicate. When I successfully implemented the shopping cart feature that persisted data across user sessions, I felt a tremendous sense of accomplishment.

    As I progressed through my internship, I was given the opportunity to work on backend development using Node.js and Express.js. This was initially intimidating because server-side programming was relatively new to me. However, with the patient guidance of my mentor, I learned to create RESTful APIs, handle database operations, and implement authentication systems. Working with both SQL and NoSQL databases gave me a comprehensive understanding of data storage and retrieval strategies.

    One of the most significant projects I contributed to was a healthcare management system for a local clinic. This project required careful attention to data security, user privacy, and regulatory compliance. I was responsible for developing the patient registration system and the appointment scheduling interface. The complexity of this project taught me about the importance of thorough testing, documentation, and the need to consider edge cases in software development.

    The healthcare project also introduced me to working directly with clients. I participated in requirement gathering meetings where I learned to listen carefully to client needs, ask clarifying questions, and translate business requirements into technical specifications. This experience was invaluable in developing my communication skills and understanding the business side of software development.

    Throughout my internship, I faced numerous challenges that tested my problem-solving abilities and resilience. One particularly difficult challenge was optimizing database queries for the healthcare system when it began experiencing performance issues with large datasets. I learned about database indexing, query optimization, and the importance of considering scalability from the beginning of a project. Working through this challenge with my team taught me the value of collaboration and the importance of seeking help when needed.

    Another significant challenge was managing multiple project deadlines while maintaining code quality. I learned to prioritize tasks effectively, communicate proactively about potential delays, and break large problems into smaller, manageable pieces. These time management skills proved crucial not only during the internship but will undoubtedly serve me well throughout my career.

    The collaborative nature of software development became apparent as I worked on various projects. Code reviews became a regular part of my routine, and I learned to both give and receive constructive feedback. Initially, having my code reviewed by senior developers was intimidating, but I quickly realized that this process was essential for improving code quality and learning best practices. Eventually, I was even asked to review code from newer interns, which helped reinforce my own learning and develop my mentoring skills.

    One of the most rewarding aspects of my internship was seeing the real-world impact of the software I helped create. When the e-commerce platform I worked on went live and started processing actual customer orders, I felt a deep sense of pride and accomplishment. Similarly, knowing that the healthcare management system I contributed to was helping a clinic serve their patients more efficiently gave my work meaning beyond just technical achievement.

    The internship also exposed me to modern development practices and tools that are essential in today's software industry. I became proficient with version control using Git and GitHub, learned about continuous integration and deployment processes, and gained experience with project management tools like Jira and Trello. These tools and practices are now second nature to me and have prepared me for working in any professional development environment.

    As my internship progressed, I was given increasing responsibility and autonomy. By the final weeks, I was leading small development tasks and mentoring newer interns who joined the program. This progression gave me confidence in my abilities and helped me understand my own growth throughout the experience. The trust that my supervisors placed in me to handle important tasks and guide others was both humbling and motivating.

    The technical skills I developed during this internship are substantial. I became proficient in React.js for frontend development, learning advanced concepts like hooks, context API, and performance optimization. My backend skills with Node.js and Express.js grew to include complex API development, authentication systems, and database integration. I also gained experience with testing frameworks, deployment processes, and cloud platforms.

    Beyond technical skills, the professional growth I experienced was equally valuable. I learned to communicate effectively with team members, clients, and stakeholders. My presentation skills improved through regular project updates and client meetings. I developed better time management abilities and learned to work effectively under pressure while maintaining attention to detail.

    The mentorship I received throughout the internship was exceptional. My supervisor and senior team members were always available to answer questions, provide guidance, and share their experiences. They taught me not just how to code, but how to think like a professional developer. The importance of writing maintainable code, considering user experience, and thinking about long-term implications of technical decisions became ingrained in my approach to development.

    Looking back on this experience, I can confidently say that this internship has fundamentally changed my understanding of software development and my career aspirations. The practical experience I gained has made me a more confident and capable developer. The challenges I overcame have strengthened my problem-solving abilities and resilience. The professional relationships I built have provided me with a network of mentors and colleagues who continue to support my growth.

    This internship has also clarified my career goals and given me a clear direction for my future professional development. I now have a much better understanding of what it means to be a full-stack developer and the skills required to succeed in this field. The experience has motivated me to continue learning and growing, and I am excited about the opportunities that lie ahead in my career.

    In conclusion, my 300-hour internship at EdnSoftTech Web Solutions Company has been an invaluable experience that has prepared me for success in the software development industry. The combination of technical skill development, professional growth, and real-world project experience has given me a strong foundation for my future career. I am grateful for the opportunity to have been part of such a supportive and educational environment, and I look forward to applying the knowledge and skills I gained in my future endeavors.

    The relationships I built, the challenges I overcame, and the projects I contributed to have all played a role in shaping me into a more confident and capable developer. This experience has not only enhanced my technical abilities but has also developed my professional skills and given me a clear vision for my career path. I am excited to continue building upon this foundation as I complete my studies and begin my professional career in software development.
    """

    doc.add_paragraph(narrative_essay.strip())

    doc.add_paragraph()
    doc.add_paragraph()

    # Signature section
    signature_section = doc.add_paragraph()
    signature_section.alignment = WD_ALIGN_PARAGRAPH.RIGHT
    signature_section.add_run('Respectfully submitted,\n\n\n')
    signature_section.add_run('_________________________\n')
    signature_section.add_run('Jordan M. Bunuan\n').bold = True
    signature_section.add_run('Computer Science Student\n')
    signature_section.add_run(f'Date: {datetime.now().strftime("%B %d, %Y")}')

    # Save the document
    output_file = r"C:\Users\<USER>\Desktop\DTR_Report_Generator\My_OJT_Experience_Simple.docx"
    doc.save(output_file)

    print(f"✅ OJT Experience document created: {output_file}")
    return output_file

if __name__ == "__main__":
    create_ojt_experience_document()
