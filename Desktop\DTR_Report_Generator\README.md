# DTR to Word Document Generator

This project converts your Daily Time Record (DTR) CSV data into a professional Word document suitable for school submission.

## Features

- **Professional Formatting**: Clean, academic-style document layout
- **Executive Summary**: Key statistics and overview of your internship
- **Detailed Task Log**: Comprehensive table of all your daily activities
- **Achievements Section**: Highlights your key accomplishments and learning outcomes
- **Automatic Analysis**: Calculates completion rates, working days, and task categories

## Requirements

Install the required Python packages:

```bash
pip install pandas python-docx numpy openpyxl
```

## Quick Start

1. **Install Dependencies**:
   ```bash
   pip install pandas python-docx numpy
   ```

2. **Run the Generator**:
   ```bash
   python dtr_to_word.py
   ```

3. **Find Your Report**:
   The generated Word document will be saved to your Desktop as `DTR_Report_Jordan_Bunuan.docx`

## What the Generated Report Includes

### 1. Header Section
- Document title
- Your name and details
- Report generation date

### 2. Internship Summary
- Total working days
- Task completion statistics
- Overall performance metrics
- Date range of internship

### 3. Detailed Task Log
- Chronological list of all tasks
- Date, time, and task descriptions
- Status of each task (DONE, PENDING, etc.)
- Professional table formatting

### 4. Key Achievements & Learning Outcomes
- Automated analysis of your task types
- UI/UX improvement contributions
- Bug fixes and technical solutions
- Development and implementation work
- Professional skills gained

### 5. Conclusion Section
- Professional summary statement
- Signature section with your name and date

## Customization

You can modify the script to:
- Change the output file location
- Adjust the CSV file path
- Customize the document formatting
- Add additional sections
- Modify the analysis criteria

## File Structure

```
DTR_Report_Generator/
├── dtr_to_word.py          # Main generator script
├── README.md               # This file
├── requirements.txt        # Python dependencies
└── sample_output.md        # Example of what the report looks like
```

## Troubleshooting

**CSV File Not Found**: Make sure your DTR CSV file is in the correct location or update the path in the script.

**Missing Dependencies**: Run `pip install -r requirements.txt` to install all required packages.

**Permission Error**: Make sure you have write permissions to the output directory.

## Example Output

The generated Word document will be professionally formatted and include:
- Clean typography using Calibri font
- Proper headings and sections
- Well-organized tables
- Professional layout suitable for academic submission

Perfect for internship reports, OJT documentation, and academic requirements!
