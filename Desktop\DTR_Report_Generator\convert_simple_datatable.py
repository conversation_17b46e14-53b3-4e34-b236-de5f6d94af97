#!/usr/bin/env python3
"""
Convert Simple DataTable Format to Word Document
Creates a clean, professional document exactly like the sample format.
"""

import re
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.oxml.shared import OxmlElement, qn

def add_simple_borders(table):
    """Add clean borders similar to the sample"""
    for cell in table._cells:
        tc = cell._tc
        tcPr = tc.get_or_add_tcPr()
        tcBorders = OxmlElement('w:tcBorders')
        for border_name in ['top', 'left', 'bottom', 'right']:
            border = OxmlElement(f'w:{border_name}')
            border.set(qn('w:val'), 'single')
            border.set(qn('w:sz'), '4')
            border.set(qn('w:space'), '0')
            border.set(qn('w:color'), '000000')
            tcBorders.append(border)
        tcPr.append(tcBorders)

def convert_simple_datatable(markdown_file, output_file):
    """Convert simple datatable to Word document matching sample format"""
    
    with open(markdown_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    doc = Document()
    
    # Set narrow margins like the sample
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(0.5)
        section.bottom_margin = Inches(0.5)
        section.left_margin = Inches(0.5)
        section.right_margin = Inches(0.5)
    
    # Add title
    title = doc.add_paragraph()
    title_run = title.add_run("Daily Time Record and Accomplishment Report")
    title_run.font.name = 'Calibri'
    title_run.font.size = Pt(14)
    title_run.bold = True
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Add intern info
    info = doc.add_paragraph()
    info_run = info.add_run("Jordan M Bunuan | May-June 2025 | 304 Hours Completed")
    info_run.font.name = 'Calibri'
    info_run.font.size = Pt(11)
    info.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_paragraph()  # Space
    
    # Find the main daily task table
    lines = content.split('\n')
    i = 0
    
    while i < len(lines):
        line = lines[i].strip()
        
        # Look for the main daily task table
        if line.startswith('|') and 'Task Accomplishments' in line:
            # Parse the table
            table_lines = []
            while i < len(lines) and lines[i].strip().startswith('|'):
                table_lines.append(lines[i].strip())
                i += 1
            
            if len(table_lines) >= 3:
                # Create table with 3 columns like the sample
                table = doc.add_table(rows=0, cols=3)
                table.alignment = WD_TABLE_ALIGNMENT.CENTER
                
                # Set column widths to match sample proportions
                table.columns[0].width = Inches(0.8)   # Date
                table.columns[1].width = Inches(4.2)   # Tasks
                table.columns[2].width = Inches(0.8)   # Hours
                
                # Skip header and separator, go straight to data
                data_lines = table_lines[2:]
                
                for data_line in data_lines:
                    if data_line.strip() and data_line.startswith('|'):
                        cells_data = [cell.strip() for cell in data_line.split('|')[1:-1]]
                        if len(cells_data) == 3:
                            # Add row
                            row = table.add_row()
                            cells = row.cells
                            
                            # Date cell - bold and centered like sample
                            cells[0].text = cells_data[0]
                            date_para = cells[0].paragraphs[0]
                            date_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                            for run in date_para.runs:
                                run.font.name = 'Calibri'
                                run.font.size = Pt(9)
                                run.bold = True
                            
                            # Tasks cell - clean bullet points
                            tasks_text = cells_data[1].replace('<br>', '\n')
                            cells[1].text = tasks_text
                            task_para = cells[1].paragraphs[0]
                            task_para.alignment = WD_ALIGN_PARAGRAPH.LEFT
                            for run in task_para.runs:
                                run.font.name = 'Calibri'
                                run.font.size = Pt(9)
                            
                            # Hours cell - bold and centered
                            cells[2].text = cells_data[2]
                            hours_para = cells[2].paragraphs[0]
                            hours_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                            for run in hours_para.runs:
                                run.font.name = 'Calibri'
                                run.font.size = Pt(9)
                                run.bold = True
                
                # Add borders
                add_simple_borders(table)
                
                # Add some space after table
                doc.add_paragraph()
                
                # Add summary section
                summary = doc.add_paragraph()
                summary_run = summary.add_run("Summary: 304 Total Hours | 38 Working Days | 101.3% Completion Rate")
                summary_run.font.name = 'Calibri'
                summary_run.font.size = Pt(11)
                summary_run.bold = True
                summary.alignment = WD_ALIGN_PARAGRAPH.CENTER
                
                break
        
        i += 1
    
    # Save the document
    doc.save(output_file)
    print(f"Simple datatable document saved as: {output_file}")

if __name__ == "__main__":
    markdown_file = "Jordan_Simple_DataTable_Format.md"
    output_file = "Jordan_Simple_DataTable_Format.docx"
    
    try:
        convert_simple_datatable(markdown_file, output_file)
        print("Conversion completed successfully!")
        print("Document format matches your sample with clean borders and layout.")
    except Exception as e:
        print(f"Error during conversion: {e}")
