#!/usr/bin/env python3
"""
Convert DTR CSV to Professional Word Document
This script converts Jordan's DTR CSV file to a professional Word document format.
"""

import csv
import re
from datetime import datetime
from docx import Document
from docx.shared import Inches, Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.oxml.shared import OxmlElement, qn

def add_table_borders(table):
    """Add professional borders to table"""
    for cell in table._cells:
        tc = cell._tc
        tcPr = tc.get_or_add_tcPr()
        tcBorders = OxmlElement('w:tcBorders')
        for border_name in ['top', 'left', 'bottom', 'right']:
            border = OxmlElement(f'w:{border_name}')
            border.set(qn('w:val'), 'single')
            border.set(qn('w:sz'), '6')
            border.set(qn('w:space'), '0')
            border.set(qn('w:color'), '000000')
            tcBorders.append(border)
        tcPr.append(tcBorders)

def parse_csv_data(csv_file):
    """Parse the DTR CSV file and extract relevant data"""
    time_records = []
    task_reports = []
    
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        rows = list(reader)
    
    # Extract basic info from first few rows
    intern_name = "Jordan M Bunuan"
    total_hours = "304"
    total_lates = "94"
    
    # Parse time records (starting from row 4, index 3)
    for i in range(4, len(rows)):
        row = rows[i]
        if len(row) >= 8 and row[0] and row[0] != "":
            # Time record data
            date = row[0]
            time_in = row[1]
            time_out = row[2]
            breaks = row[3]
            total_hrs = row[4]
            lates = row[5] if row[5] else ""
            notes = row[6] if row[6] else ""
            
            time_records.append({
                'date': date,
                'time_in': time_in,
                'time_out': time_out,
                'breaks': breaks,
                'total_hours': total_hrs,
                'lates': lates,
                'notes': notes
            })
        
        # Task report data (columns 8-13)
        if len(row) >= 14 and row[8] and row[8] != "":
            task_date = row[8]
            task_start = row[9]
            task_end = row[10]
            task_desc = row[11]
            status = row[12]
            account = row[13] if len(row) > 13 else ""
            
            task_reports.append({
                'date': task_date,
                'time_start': task_start,
                'time_end': task_end,
                'task': task_desc,
                'status': status,
                'account': account
            })
    
    return {
        'intern_name': intern_name,
        'total_hours': total_hours,
        'total_lates': total_lates,
        'time_records': time_records,
        'task_reports': task_reports
    }

def convert_dtr_csv_to_docx(csv_file, output_file):
    """Convert DTR CSV to professional Word document"""
    
    # Parse CSV data
    data = parse_csv_data(csv_file)
    
    # Create a new Document
    doc = Document()
    
    # Set document margins
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(0.75)
        section.bottom_margin = Inches(0.75)
        section.left_margin = Inches(0.75)
        section.right_margin = Inches(0.75)
    
    # Add custom styles
    styles = doc.styles
    
    # Title style
    title_style = styles.add_style('CustomTitle', WD_STYLE_TYPE.PARAGRAPH)
    title_style.font.name = 'Calibri'
    title_style.font.size = Pt(16)
    title_style.font.bold = True
    title_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
    title_style.paragraph_format.space_after = Pt(12)
    
    # Heading style
    h2_style = styles.add_style('CustomH2', WD_STYLE_TYPE.PARAGRAPH)
    h2_style.font.name = 'Calibri'
    h2_style.font.size = Pt(14)
    h2_style.font.bold = True
    h2_style.paragraph_format.space_before = Pt(12)
    h2_style.paragraph_format.space_after = Pt(6)
    
    # Body text style
    body_style = styles.add_style('CustomBody', WD_STYLE_TYPE.PARAGRAPH)
    body_style.font.name = 'Calibri'
    body_style.font.size = Pt(11)
    body_style.paragraph_format.space_after = Pt(3)
    
    # Add title
    title = doc.add_paragraph("DAILY TIME RECORD AND ACCOMPLISHMENT REPORT", style='CustomTitle')
    
    # Add intern information
    info_para = doc.add_paragraph()
    info_para.add_run("Intern Name: ").bold = True
    info_para.add_run(f"{data['intern_name']}\n")
    info_para.add_run("Total Hours Completed: ").bold = True
    info_para.add_run(f"{data['total_hours']} Hours\n")
    info_para.add_run("Total Lates: ").bold = True
    info_para.add_run(f"{data['total_lates']} minutes")
    info_para.style = body_style
    
    doc.add_paragraph()
    
    # Add Daily Time IN-OUT section
    doc.add_paragraph("Daily Time IN-OUT", style='CustomH2')
    
    # Create time records table
    time_table = doc.add_table(rows=1, cols=7)
    time_table.alignment = WD_TABLE_ALIGNMENT.CENTER
    
    # Set column widths for time table
    time_table.columns[0].width = Inches(1.0)   # Date
    time_table.columns[1].width = Inches(1.0)   # Time In
    time_table.columns[2].width = Inches(1.0)   # Time Out
    time_table.columns[3].width = Inches(0.8)   # Breaks
    time_table.columns[4].width = Inches(0.8)   # Total Hours
    time_table.columns[5].width = Inches(0.8)   # Lates
    time_table.columns[6].width = Inches(2.5)   # Notes
    
    # Add time table headers
    time_headers = ['Date', 'Time In', 'Time Out', 'Breaks (Hours)', 'Total Hours', 'Lates(minutes)', 'Notes']
    hdr_cells = time_table.rows[0].cells
    for j, header_text in enumerate(time_headers):
        hdr_cells[j].text = header_text
        # Format header cells
        for paragraph in hdr_cells[j].paragraphs:
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            for run in paragraph.runs:
                run.bold = True
                run.font.size = Pt(9)
                run.font.name = 'Calibri'
        
        # Add gray background to header
        shading_elm = OxmlElement('w:shd')
        shading_elm.set(qn('w:fill'), 'D9D9D9')
        hdr_cells[j]._tc.get_or_add_tcPr().append(shading_elm)
    
    # Add time record data
    for record in data['time_records']:
        row_cells = time_table.add_row().cells
        row_cells[0].text = record['date']
        row_cells[1].text = record['time_in']
        row_cells[2].text = record['time_out']
        row_cells[3].text = record['breaks']
        row_cells[4].text = record['total_hours']
        row_cells[5].text = record['lates']
        row_cells[6].text = record['notes']
        
        # Format data cells
        for j, cell in enumerate(row_cells):
            for paragraph in cell.paragraphs:
                if j in [0, 1, 2, 3, 4, 5]:  # Center align date, times, breaks, hours, lates
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                else:  # Left align notes
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
                for run in paragraph.runs:
                    run.font.size = Pt(8)
                    run.font.name = 'Calibri'
    
    # Add borders to time table
    add_table_borders(time_table)
    
    doc.add_paragraph()
    
    # Add Reports section
    doc.add_paragraph("Reports", style='CustomH2')
    
    # Create reports table
    reports_table = doc.add_table(rows=1, cols=6)
    reports_table.alignment = WD_TABLE_ALIGNMENT.CENTER
    
    # Set column widths for reports table
    reports_table.columns[0].width = Inches(1.0)   # Date
    reports_table.columns[1].width = Inches(1.0)   # Time Start
    reports_table.columns[2].width = Inches(1.0)   # Time End
    reports_table.columns[3].width = Inches(3.5)   # Task
    reports_table.columns[4].width = Inches(0.8)   # Status
    reports_table.columns[5].width = Inches(0.7)   # Account
    
    # Add reports table headers
    reports_headers = ['Date', 'Time Start', 'Time End', 'Task', 'Status', 'Account']
    hdr_cells = reports_table.rows[0].cells
    for j, header_text in enumerate(reports_headers):
        hdr_cells[j].text = header_text
        # Format header cells
        for paragraph in hdr_cells[j].paragraphs:
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            for run in paragraph.runs:
                run.bold = True
                run.font.size = Pt(9)
                run.font.name = 'Calibri'
        
        # Add gray background to header
        shading_elm = OxmlElement('w:shd')
        shading_elm.set(qn('w:fill'), 'D9D9D9')
        hdr_cells[j]._tc.get_or_add_tcPr().append(shading_elm)
    
    # Add task report data
    for task in data['task_reports']:
        row_cells = reports_table.add_row().cells
        row_cells[0].text = task['date']
        row_cells[1].text = task['time_start']
        row_cells[2].text = task['time_end']
        row_cells[3].text = task['task']
        row_cells[4].text = task['status']
        row_cells[5].text = task['account']
        
        # Format data cells
        for j, cell in enumerate(row_cells):
            for paragraph in cell.paragraphs:
                if j in [0, 1, 2, 4, 5]:  # Center align date, times, status, account
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                else:  # Left align task description
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
                for run in paragraph.runs:
                    run.font.size = Pt(8)
                    run.font.name = 'Calibri'
    
    # Add borders to reports table
    add_table_borders(reports_table)
    
    # Add some space before signature section
    doc.add_paragraph()
    doc.add_paragraph()
    
    # Add signature section
    signature_para = doc.add_paragraph()
    signature_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Add total hours
    total_run = signature_para.add_run("Total Hours:")
    total_run.font.size = Pt(12)
    total_run.font.name = 'Calibri'
    
    # Add spaces and total hours value
    spaces_run = signature_para.add_run(" " * 50)  # Add spacing
    hours_run = signature_para.add_run("304 Hours")
    hours_run.font.size = Pt(12)
    hours_run.font.name = 'Calibri'
    
    # Add multiple line breaks for signature space
    for _ in range(6):
        doc.add_paragraph()
    
    # Add "Noted by:" section
    noted_para = doc.add_paragraph("Noted by:")
    noted_para.style.font.size = Pt(12)
    noted_para.style.font.name = 'Calibri'
    
    # Add space for signature
    doc.add_paragraph()
    doc.add_paragraph()
    
    # Add signature line and title
    sig_para = doc.add_paragraph("_" * 30)
    sig_para.style.font.size = Pt(12)
    sig_para.style.font.name = 'Calibri'
    
    name_para = doc.add_paragraph("(Name)")
    name_para.style.font.size = Pt(12)
    name_para.style.font.name = 'Calibri'
    
    title_para = doc.add_paragraph("OJT Supervisor")
    title_para.style.font.size = Pt(12)
    title_para.style.font.name = 'Calibri'
    
    # Save the document
    doc.save(output_file)
    print(f"DTR document saved as: {output_file}")

if __name__ == "__main__":
    csv_file = "DTR-2025(Jordan) - Copy.csv"
    output_file = "Jordan-DTR.docx"
    
    try:
        convert_dtr_csv_to_docx(csv_file, output_file)
        print("Conversion completed successfully!")
        print(f"Your DTR has been converted to {output_file}")
    except Exception as e:
        print(f"Error during conversion: {e}")
        import traceback
        traceback.print_exc()
