#!/usr/bin/env python3
"""
DTR to Word Document Generator
Converts CSV DTR data to a professional Word document for school submission
"""

import pandas as pd
import numpy as np
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.oxml.shared import OxmlElement, qn
from datetime import datetime, timedelta
import os
import sys

class DTRReportGenerator:
    def __init__(self, csv_file_path):
        self.csv_file_path = csv_file_path
        self.document = Document()
        self.intern_name = "Jordan M Bunuan"
        self.setup_document_styles()
        
    def setup_document_styles(self):
        """Setup document styles and formatting"""
        # Set normal style
        style = self.document.styles['Normal']
        font = style.font
        font.name = 'Calibri'
        font.size = Pt(11)
        
        # Set title style
        title_style = self.document.styles['Title']
        title_font = title_style.font
        title_font.name = 'Calibri'
        title_font.size = Pt(16)
        title_font.bold = True

    def read_csv_data(self):
        """Read and process the CSV data"""
        try:
            # Read the CSV file
            df = pd.read_csv(self.csv_file_path, header=None)
            
            # Find the actual data start (looking for the "Reports" column)
            reports_row = None
            for idx, row in df.iterrows():
                if 'Reports' in str(row.values):
                    reports_row = idx
                    break
            
            if reports_row is None:
                print("Could not find the Reports section in the CSV")
                return None
            
            # Extract headers and data
            headers = df.iloc[reports_row].values
            data_start = reports_row + 1
            
            # Get the actual task data
            task_data = []
            for idx in range(data_start, len(df)):
                row = df.iloc[idx].values
                # Skip empty rows or rows with all NaN
                if pd.isna(row).all() or all(str(x).strip() == '' for x in row if pd.notna(x)):
                    continue
                    
                # Extract relevant columns (Date, Time Start, Time End, Task, Status)
                if len(row) > 11:  # Ensure we have enough columns
                    date = row[8] if pd.notna(row[8]) else ''
                    time_start = row[9] if pd.notna(row[9]) else ''
                    time_end = row[10] if pd.notna(row[10]) else ''
                    task = row[11] if pd.notna(row[11]) else ''
                    status = row[12] if pd.notna(row[12]) else ''
                    
                    if date and task:  # Only include rows with date and task
                        task_data.append({
                            'Date': str(date).strip(),
                            'Time Start': str(time_start).strip(),
                            'Time End': str(time_end).strip(),
                            'Task': str(task).strip(),
                            'Status': str(status).strip()
                        })
            
            return task_data
            
        except Exception as e:
            print(f"Error reading CSV file: {e}")
            return None

    def calculate_summary_stats(self, task_data):
        """Calculate summary statistics from the task data"""
        if not task_data:
            return {}
        
        total_days = len(set(entry['Date'] for entry in task_data if entry['Date']))
        completed_tasks = len([entry for entry in task_data if entry['Status'].upper() == 'DONE'])
        pending_tasks = len([entry for entry in task_data if entry['Status'].upper() in ['PENDING', 'TO BE CONTINUED']])
        total_tasks = len(task_data)
        
        # Calculate date range
        dates = [entry['Date'] for entry in task_data if entry['Date']]
        if dates:
            try:
                # Parse dates to find range
                parsed_dates = []
                for date_str in dates:
                    try:
                        # Try different date formats
                        for fmt in ['%d/%m/%Y', '%m/%d/%Y', '%Y-%m-%d']:
                            try:
                                parsed_date = datetime.strptime(date_str, fmt)
                                parsed_dates.append(parsed_date)
                                break
                            except ValueError:
                                continue
                    except:
                        continue
                
                if parsed_dates:
                    start_date = min(parsed_dates)
                    end_date = max(parsed_dates)
                else:
                    start_date = "N/A"
                    end_date = "N/A"
            except:
                start_date = dates[0] if dates else "N/A"
                end_date = dates[-1] if dates else "N/A"
        else:
            start_date = "N/A"
            end_date = "N/A"
        
        return {
            'total_days': total_days,
            'total_tasks': total_tasks,
            'completed_tasks': completed_tasks,
            'pending_tasks': pending_tasks,
            'completion_rate': f"{(completed_tasks/total_tasks*100):.1f}%" if total_tasks > 0 else "0%",
            'start_date': start_date,
            'end_date': end_date
        }

    def add_header(self):
        """Add document header with title and intern information"""
        # Add title
        title = self.document.add_heading('DAILY TIME RECORD (DTR) REPORT', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Add intern information
        self.document.add_paragraph()
        
        info_para = self.document.add_paragraph()
        info_para.add_run(f"Intern Name: {self.intern_name}").bold = True
        info_para.add_run(f"\nReport Generated: {datetime.now().strftime('%B %d, %Y')}")
        info_para.add_run(f"\nAcademic Year: 2025")
        
        self.document.add_paragraph()

    def add_summary_section(self, stats):
        """Add summary statistics section"""
        self.document.add_heading('INTERNSHIP SUMMARY', level=1)
        
        # Create summary table
        summary_table = self.document.add_table(rows=7, cols=2)
        summary_table.style = 'Table Grid'
        summary_table.alignment = WD_TABLE_ALIGNMENT.LEFT
        
        # Add summary data
        summary_data = [
            ('Total Working Days', str(stats['total_days'])),
            ('Total Tasks Completed', f"{stats['completed_tasks']} out of {stats['total_tasks']}"),
            ('Task Completion Rate', stats['completion_rate']),
            ('Pending Tasks', str(stats['pending_tasks'])),
            ('Internship Start Date', str(stats['start_date'])),
            ('Latest Entry Date', str(stats['end_date'])),
            ('Total Hours Logged', '304 hours') # From the original data
        ]
        
        for i, (label, value) in enumerate(summary_data):
            row_cells = summary_table.rows[i].cells
            row_cells[0].text = label
            row_cells[1].text = value
            
            # Make label bold
            row_cells[0].paragraphs[0].runs[0].bold = True
        
        self.document.add_paragraph()

    def add_detailed_tasks_section(self, task_data):
        """Add detailed tasks section with table"""
        self.document.add_heading('DETAILED TASK LOG', level=1)
        
        if not task_data:
            self.document.add_paragraph("No task data available.")
            return
        
        # Create tasks table
        tasks_table = self.document.add_table(rows=1, cols=5)
        tasks_table.style = 'Table Grid'
        tasks_table.alignment = WD_TABLE_ALIGNMENT.LEFT
        
        # Add headers
        header_cells = tasks_table.rows[0].cells
        headers = ['Date', 'Time Start', 'Time End', 'Task Description', 'Status']
        for i, header in enumerate(headers):
            header_cells[i].text = header
            header_cells[i].paragraphs[0].runs[0].bold = True
        
        # Add task data (limit to prevent overly long document)
        max_tasks = 50  # Limit for readability
        displayed_tasks = task_data[:max_tasks] if len(task_data) > max_tasks else task_data
        
        for task in displayed_tasks:
            row_cells = tasks_table.add_row().cells
            row_cells[0].text = task['Date']
            row_cells[1].text = task['Time Start']
            row_cells[2].text = task['Time End']
            row_cells[3].text = task['Task'][:100] + "..." if len(task['Task']) > 100 else task['Task']  # Truncate long tasks
            row_cells[4].text = task['Status']
        
        if len(task_data) > max_tasks:
            self.document.add_paragraph(f"\nNote: Showing first {max_tasks} tasks out of {len(task_data)} total tasks.")
        
        self.document.add_paragraph()

    def add_achievements_section(self, task_data):
        """Add key achievements and learning section"""
        self.document.add_heading('KEY ACHIEVEMENTS & LEARNING OUTCOMES', level=1)
        
        # Analyze tasks to extract key achievements
        achievements = []
        
        # Count different types of tasks
        ui_tasks = len([t for t in task_data if any(keyword in t['Task'].lower() 
                       for keyword in ['ui', 'css', 'styling', 'alignment', 'design'])])
        bug_fixes = len([t for t in task_data if any(keyword in t['Task'].lower() 
                        for keyword in ['fix', 'bug', 'error', 'troubleshoot'])])
        dev_tasks = len([t for t in task_data if any(keyword in t['Task'].lower() 
                        for keyword in ['implement', 'develop', 'create', 'build'])])
        
        if ui_tasks > 0:
            achievements.append(f"Completed {ui_tasks} UI/UX improvement tasks, enhancing user experience and interface design")
        
        if bug_fixes > 0:
            achievements.append(f"Successfully resolved {bug_fixes} technical issues and bugs, improving system stability")
        
        if dev_tasks > 0:
            achievements.append(f"Implemented {dev_tasks} new features and development tasks, contributing to project growth")
        
        # Add general achievements
        achievements.extend([
            "Gained hands-on experience with WordPress development and plugin management",
            "Developed proficiency in web technologies including CSS, JavaScript, and PHP",
            "Enhanced problem-solving skills through systematic troubleshooting approaches",
            "Improved project management and time tracking capabilities",
            "Collaborated effectively with team members and supervisors"
        ])
        
        for achievement in achievements:
            para = self.document.add_paragraph()
            para.style = 'List Bullet'
            para.add_run(achievement)
        
        self.document.add_paragraph()

    def add_footer_section(self):
        """Add concluding section"""
        self.document.add_heading('CONCLUSION', level=1)
        
        conclusion_text = """
This Daily Time Record represents my commitment to professional development and learning during my internship period. 
Through consistent documentation and task completion, I have demonstrated reliability, technical growth, and dedication 
to contributing meaningfully to the organization's objectives.

The experience gained through various development tasks, troubleshooting activities, and project contributions has 
significantly enhanced my technical skills and professional capabilities, preparing me for future career opportunities 
in software development and IT.
        """
        
        para = self.document.add_paragraph(conclusion_text.strip())
        para.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
        
        # Add signature section
        self.document.add_paragraph()
        self.document.add_paragraph()
        
        signature_para = self.document.add_paragraph()
        signature_para.add_run("Prepared by: ").bold = True
        signature_para.add_run(f"\n\n{self.intern_name}")
        signature_para.add_run("\nIntern")
        signature_para.add_run(f"\nDate: {datetime.now().strftime('%B %d, %Y')}")

    def generate_report(self, output_path=None):
        """Generate the complete DTR report"""
        print("Reading CSV data...")
        task_data = self.read_csv_data()
        
        if not task_data:
            print("Failed to read task data from CSV")
            return False
        
        print(f"Found {len(task_data)} task entries")
        
        # Calculate statistics
        stats = self.calculate_summary_stats(task_data)
        
        print("Generating Word document...")
        
        # Add document sections
        self.add_header()
        self.add_summary_section(stats)
        self.add_detailed_tasks_section(task_data)
        self.add_achievements_section(task_data)
        self.add_footer_section()
        
        # Save document
        if output_path is None:
            output_path = f"DTR_Report_{self.intern_name.replace(' ', '_')}_{datetime.now().strftime('%Y%m%d')}.docx"
        
        try:
            self.document.save(output_path)
            print(f"Report successfully generated: {output_path}")
            return True
        except Exception as e:
            print(f"Error saving document: {e}")
            return False

def main():
    """Main function to run the DTR report generator"""
    # Default CSV file path (you can modify this)
    csv_file_path = r"c:\Users\<USER>\Downloads\DTR-2025(Jordan) - Copy.csv"
    
    # Check if CSV file exists
    if not os.path.exists(csv_file_path):
        print(f"CSV file not found: {csv_file_path}")
        print("Please ensure the CSV file exists or update the file path in the script.")
        return
    
    # Generate report
    generator = DTRReportGenerator(csv_file_path)
    
    # Output file path
    output_path = r"C:\Users\<USER>\Desktop\DTR_Report_Jordan_Bunuan.docx"
    
    success = generator.generate_report(output_path)
    
    if success:
        print("\n✅ DTR Report generated successfully!")
        print(f"📁 Saved to: {output_path}")
        print("\nThe Word document includes:")
        print("• Executive Summary")
        print("• Detailed Task Log")
        print("• Key Achievements")
        print("• Professional Formatting")
    else:
        print("\n❌ Failed to generate DTR report")

if __name__ == "__main__":
    main()
