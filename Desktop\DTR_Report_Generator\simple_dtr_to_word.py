#!/usr/bin/env python3
"""
Simple DTR to Word Generator
Quick conversion of CSV DTR data to Word document
"""

import pandas as pd
from docx import Document
from docx.shared import Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from datetime import datetime

def create_dtr_report():
    # File paths
    csv_file = r"c:\Users\<USER>\Downloads\DTR-2025(Jordan) - Copy.csv"
    output_file = r"C:\Users\<USER>\Desktop\DTR_Report_Simple.docx"
    
    # Create document
    doc = Document()
    
    # Title
    title = doc.add_heading('DAILY TIME RECORD REPORT', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Basic info
    doc.add_paragraph(f"Student: Jordan M Bunuan")
    doc.add_paragraph(f"Date Generated: {datetime.now().strftime('%B %d, %Y')}")
    doc.add_paragraph()
    
    # Read CSV and extract tasks
    try:
        df = pd.read_csv(csv_file, header=None)
        tasks = []
        
        for idx, row in df.iterrows():
            row_values = row.values
            if len(row_values) > 11 and pd.notna(row_values[8]) and pd.notna(row_values[11]):
                date = str(row_values[8]).strip()
                task = str(row_values[11]).strip()
                status = str(row_values[12]).strip() if pd.notna(row_values[12]) else 'N/A'
                
                if date and task and not task.startswith('Task'):
                    tasks.append({'date': date, 'task': task, 'status': status})
        
        # Summary
        doc.add_heading('Summary', level=1)
        doc.add_paragraph(f"Total Tasks: {len(tasks)}")
        completed = len([t for t in tasks if t['status'].upper() == 'DONE'])
        doc.add_paragraph(f"Completed Tasks: {completed}")
        doc.add_paragraph(f"Completion Rate: {(completed/len(tasks)*100):.1f}%" if tasks else "0%")
        doc.add_paragraph()
        
        # Task list (first 30 tasks)
        doc.add_heading('Task Details', level=1)
        
        display_tasks = tasks[:30] if len(tasks) > 30 else tasks
        
        for task in display_tasks:
            para = doc.add_paragraph()
            para.add_run(f"{task['date']}: ").bold = True
            para.add_run(f"{task['task']} ")
            para.add_run(f"[{task['status']}]").italic = True
        
        if len(tasks) > 30:
            doc.add_paragraph(f"\n... and {len(tasks)-30} more tasks")
        
        # Save
        doc.save(output_file)
        print(f"✅ Report saved to: {output_file}")
        print(f"📊 Processed {len(tasks)} tasks")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    create_dtr_report()
    doc = Document()
    
    # Title
    title = doc.add_heading('DAILY TIME RECORD REPORT', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Basic info
    doc.add_paragraph(f"Student: Jordan M Bunuan")
    doc.add_paragraph(f"Date Generated: {datetime.now().strftime('%B %d, %Y')}")
    doc.add_paragraph()
    
    # Read CSV and extract tasks
    try:
        df = pd.read_csv(csv_file, header=None)
        tasks = []
        
        for idx, row in df.iterrows():
            row_values = row.values
            if len(row_values) > 11 and pd.notna(row_values[8]) and pd.notna(row_values[11]):
                date = str(row_values[8]).strip()
                task = str(row_values[11]).strip()
                status = str(row_values[12]).strip() if pd.notna(row_values[12]) else 'N/A'
                
                if date and task and not task.startswith('Task'):
                    tasks.append({'date': date, 'task': task, 'status': status})
        
        # Summary
        doc.add_heading('Summary', level=1)
        doc.add_paragraph(f"Total Tasks: {len(tasks)}")
        completed = len([t for t in tasks if t['status'].upper() == 'DONE'])
        doc.add_paragraph(f"Completed Tasks: {completed}")
        doc.add_paragraph(f"Completion Rate: {(completed/len(tasks)*100):.1f}%" if tasks else "0%")
        doc.add_paragraph()
        
        # Task list (first 30 tasks)
        doc.add_heading('Task Details', level=1)
        
        display_tasks = tasks[:30] if len(tasks) > 30 else tasks
        
        for task in display_tasks:
            para = doc.add_paragraph()
            para.add_run(f"{task['date']}: ").bold = True
            para.add_run(f"{task['task']} ")
            para.add_run(f"[{task['status']}]").italic = True
        
        if len(tasks) > 30:
            doc.add_paragraph(f"\n... and {len(tasks)-30} more tasks")
        
        # Save
        doc.save(output_file)
        print(f"✅ Report saved to: {output_file}")
        print(f"📊 Processed {len(tasks)} tasks")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    create_dtr_report()
