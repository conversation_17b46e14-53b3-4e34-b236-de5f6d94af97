#!/usr/bin/env python3
"""
Simple DTR to Word Generator - COMPLETE VERSION
Quick conversion of CSV DTR data to Word document
"""

import pandas as pd
from docx import Document
from docx.shared import Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from datetime import datetime

def create_dtr_report():
    # File paths
    csv_file = r"c:\Users\<USER>\Downloads\DTR-2025(Jordan) - Copy.csv"
    output_file = r"C:\Users\<USER>\Desktop\DTR_Report_Simple.docx"
    
    print("Creating DTR Word document...")
    
    # Create document
    doc = Document()
    
    # Title
    title = doc.add_heading('DAILY TIME RECORD REPORT', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Basic info
    doc.add_paragraph(f"Student: Jordan M Bunuan")
    doc.add_paragraph(f"Date Generated: {datetime.now().strftime('%B %d, %Y')}")
    doc.add_paragraph()
    
    # Read CSV and extract tasks
    try:
        df = pd.read_csv(csv_file, header=None)
        tasks = []
        
        for idx, row in df.iterrows():
            row_values = row.values
            if len(row_values) > 11 and pd.notna(row_values[8]) and pd.notna(row_values[11]):
                date = str(row_values[8]).strip()
                task = str(row_values[11]).strip()
                status = str(row_values[12]).strip() if pd.notna(row_values[12]) else 'N/A'
                
                if date and task and not task.startswith('Task') and '/' in date:
                    tasks.append({'date': date, 'task': task, 'status': status})
        
        # Summary section
        doc.add_heading('Summary', level=1)
        doc.add_paragraph(f"Total Tasks: {len(tasks)}")
        completed = len([t for t in tasks if t['status'].upper() == 'DONE'])
        doc.add_paragraph(f"Completed Tasks: {completed}")
        doc.add_paragraph(f"Completion Rate: {(completed/len(tasks)*100):.1f}%" if tasks else "0%")
        doc.add_paragraph()
        
        # Task details section
        doc.add_heading('Task Details', level=1)
        
        # Show first 25 tasks to keep document manageable
        display_tasks = tasks[:25] if len(tasks) > 25 else tasks
        
        for i, task in enumerate(display_tasks, 1):
            para = doc.add_paragraph()
            para.add_run(f"{i}. {task['date']}: ").bold = True
            para.add_run(f"{task['task']} ")
            para.add_run(f"[{task['status']}]").italic = True
        
        if len(tasks) > 25:
            doc.add_paragraph(f"\n... and {len(tasks)-25} more tasks (showing first 25 for brevity)")
        
        # Footer
        doc.add_paragraph()
        footer = doc.add_paragraph(f"Report generated on {datetime.now().strftime('%B %d, %Y at %I:%M %p')}")
        footer.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        
        # Save document
        doc.save(output_file)
        print(f"✅ Word report saved to: {output_file}")
        print(f"📊 Processed {len(tasks)} tasks")
        print(f"✅ Completed: {completed} tasks ({(completed/len(tasks)*100):.1f}%)")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Make sure pandas and python-docx are installed:")
        print("pip install pandas python-docx")

if __name__ == "__main__":
    create_dtr_report()
