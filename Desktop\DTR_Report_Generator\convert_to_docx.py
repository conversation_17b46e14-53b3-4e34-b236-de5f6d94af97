#!/usr/bin/env python3
"""
Convert Markdown Daily Report to Word Document
This script converts the detailed markdown report to a professionally formatted Word document.
"""

import re
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn

def add_table_borders(table):
    """Add borders to table"""
    tbl = table._tbl
    for cell in table._cells:
        tc = cell._tc
        tcPr = tc.get_or_add_tcPr()
        tcBorders = OxmlElement('w:tcBorders')
        for border_name in ['top', 'left', 'bottom', 'right']:
            border = OxmlElement(f'w:{border_name}')
            border.set(qn('w:val'), 'single')
            border.set(qn('w:sz'), '4')
            border.set(qn('w:space'), '0')
            border.set(qn('w:color'), '000000')
            tcBorders.append(border)
        tcPr.append(tcBorders)

def convert_markdown_to_docx(markdown_file, output_file):
    """Convert markdown file to Word document with professional formatting"""

    # Read the markdown file
    with open(markdown_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # Create a new Document
    doc = Document()

    # Set document margins
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(0.8)
        section.bottom_margin = Inches(0.8)
        section.left_margin = Inches(1)
        section.right_margin = Inches(1)

    # Add header
    header = sections[0].header
    header_para = header.paragraphs[0]
    header_para.text = "Jordan M Bunuan - Daily Time Record and Accomplishment Report"
    header_para.style.font.size = Pt(10)
    header_para.style.font.italic = True

    # Add footer with page numbers
    footer = sections[0].footer
    footer_para = footer.paragraphs[0]
    footer_para.text = "Page "
    footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Add custom styles
    styles = doc.styles
    
    # Title style
    title_style = styles.add_style('CustomTitle', WD_STYLE_TYPE.PARAGRAPH)
    title_style.font.name = 'Calibri'
    title_style.font.size = Pt(18)
    title_style.font.bold = True
    title_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
    title_style.paragraph_format.space_after = Pt(12)
    
    # Heading 1 style
    h1_style = styles.add_style('CustomH1', WD_STYLE_TYPE.PARAGRAPH)
    h1_style.font.name = 'Calibri'
    h1_style.font.size = Pt(16)
    h1_style.font.bold = True
    h1_style.paragraph_format.space_before = Pt(12)
    h1_style.paragraph_format.space_after = Pt(6)
    
    # Heading 2 style
    h2_style = styles.add_style('CustomH2', WD_STYLE_TYPE.PARAGRAPH)
    h2_style.font.name = 'Calibri'
    h2_style.font.size = Pt(14)
    h2_style.font.bold = True
    h2_style.paragraph_format.space_before = Pt(10)
    h2_style.paragraph_format.space_after = Pt(4)
    
    # Heading 3 style
    h3_style = styles.add_style('CustomH3', WD_STYLE_TYPE.PARAGRAPH)
    h3_style.font.name = 'Calibri'
    h3_style.font.size = Pt(12)
    h3_style.font.bold = True
    h3_style.paragraph_format.space_before = Pt(8)
    h3_style.paragraph_format.space_after = Pt(4)
    
    # Body text style
    body_style = styles.add_style('CustomBody', WD_STYLE_TYPE.PARAGRAPH)
    body_style.font.name = 'Calibri'
    body_style.font.size = Pt(11)
    body_style.paragraph_format.space_after = Pt(6)
    
    # Split content into lines
    lines = content.split('\n')
    i = 0
    
    while i < len(lines):
        line = lines[i].strip()
        
        if not line:
            i += 1
            continue
            
        # Handle main title
        if line.startswith('# '):
            p = doc.add_paragraph(line[2:], style='CustomTitle')
            
        # Handle level 2 headings
        elif line.startswith('## '):
            p = doc.add_paragraph(line[3:], style='CustomH1')
            
        # Handle level 3 headings
        elif line.startswith('### '):
            p = doc.add_paragraph(line[4:], style='CustomH2')
            
        # Handle level 4 headings
        elif line.startswith('#### '):
            p = doc.add_paragraph(line[5:], style='CustomH3')
            
        # Handle tables
        elif line.startswith('|') and '|' in line:
            # Parse table
            table_lines = []
            while i < len(lines) and lines[i].strip().startswith('|'):
                table_lines.append(lines[i].strip())
                i += 1
            i -= 1  # Back up one since we'll increment at the end

            if len(table_lines) >= 2:
                # Parse header
                header = [cell.strip() for cell in table_lines[0].split('|')[1:-1]]

                # Skip separator line
                data_lines = table_lines[2:] if len(table_lines) > 2 else []

                # Create table
                table = doc.add_table(rows=1, cols=len(header))
                table.style = 'Light Grid Accent 1'

                # Set table alignment
                table.alignment = WD_ALIGN_PARAGRAPH.CENTER

                # Add header with formatting
                hdr_cells = table.rows[0].cells
                for j, header_text in enumerate(header):
                    hdr_cells[j].text = header_text
                    # Format header cells
                    for paragraph in hdr_cells[j].paragraphs:
                        for run in paragraph.runs:
                            run.bold = True
                            run.font.size = Pt(10)
                            run.font.name = 'Calibri'
                        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

                # Add data rows
                for data_line in data_lines:
                    if data_line.strip():
                        cells_data = [cell.strip() for cell in data_line.split('|')[1:-1]]
                        if len(cells_data) == len(header):
                            row_cells = table.add_row().cells
                            for j, cell_data in enumerate(cells_data):
                                row_cells[j].text = cell_data
                                # Format data cells
                                for paragraph in row_cells[j].paragraphs:
                                    for run in paragraph.runs:
                                        run.font.size = Pt(9)
                                        run.font.name = 'Calibri'
                                    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT

                # Add borders and formatting
                add_table_borders(table)

                # Add spacing after table
                doc.add_paragraph()
        
        # Handle bullet points
        elif line.startswith('- '):
            p = doc.add_paragraph(line[2:], style='List Bullet')
            p.style.font.name = 'Calibri'
            p.style.font.size = Pt(11)
            
        # Handle numbered lists
        elif re.match(r'^\d+\.', line):
            p = doc.add_paragraph(line, style='List Number')
            p.style.font.name = 'Calibri'
            p.style.font.size = Pt(11)
            
        # Handle bold text
        elif line.startswith('**') and line.endswith('**'):
            p = doc.add_paragraph()
            run = p.add_run(line[2:-2])
            run.bold = True
            run.font.name = 'Calibri'
            run.font.size = Pt(11)
            
        # Handle horizontal rules
        elif line.startswith('---'):
            p = doc.add_paragraph()
            p.add_run('_' * 50)
            
        # Handle regular paragraphs
        else:
            if line:
                p = doc.add_paragraph(line, style='CustomBody')
        
        i += 1
    
    # Save the document
    doc.save(output_file)
    print(f"Document saved as: {output_file}")

if __name__ == "__main__":
    markdown_file = "Jordan_Daily_Report_with_Weekly_Accomplishments.md"
    output_file = "Jordan_Daily_Report_with_Weekly_Accomplishments.docx"
    
    try:
        convert_markdown_to_docx(markdown_file, output_file)
        print("Conversion completed successfully!")
    except Exception as e:
        print(f"Error during conversion: {e}")
