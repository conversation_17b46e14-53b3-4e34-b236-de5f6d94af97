#!/usr/bin/env python3
"""
Ultra Simple DTR Report Generator
Creates a basic text report from CSV DTR data
"""

import csv
from datetime import datetime

def create_simple_report():
    # File paths
    csv_file = r"c:\Users\<USER>\Downloads\DTR-2025(Jordan) - Copy.csv"
    output_file = r"C:\Users\<USER>\Desktop\DTR_Report_Simple.txt"
    
    print("Creating simple DTR report...")
    
    # Read CSV and extract data
    tasks = []
    try:
        with open(csv_file, 'r', encoding='utf-8') as file:
            reader = csv.reader(file)
            for row in reader:
                if len(row) > 11 and row[8] and row[11]:
                    date = row[8].strip()
                    task = row[11].strip()
                    status = row[12].strip() if len(row) > 12 and row[12] else 'N/A'
                    
                    # Skip headers and empty entries
                    if date and task and not task.startswith('Task') and '/' in date:
                        tasks.append({
                            'date': date,
                            'task': task,
                            'status': status
                        })
    
        # Create report
        with open(output_file, 'w', encoding='utf-8') as file:
            file.write("DAILY TIME RECORD (DTR) REPORT\n")
            file.write("="*50 + "\n\n")
            file.write(f"Student: Jordan M Bunuan\n")
            file.write(f"Report Generated: {datetime.now().strftime('%B %d, %Y')}\n\n")
            
            # Summary
            file.write("SUMMARY\n")
            file.write("-"*20 + "\n")
            file.write(f"Total Tasks: {len(tasks)}\n")
            
            completed = len([t for t in tasks if t['status'].upper() == 'DONE'])
            file.write(f"Completed Tasks: {completed}\n")
            file.write(f"Completion Rate: {(completed/len(tasks)*100):.1f}%\n" if tasks else "Completion Rate: 0%\n")
            file.write("\n")
            
            # Task details
            file.write("TASK DETAILS\n")
            file.write("-"*20 + "\n")
            
            for i, task in enumerate(tasks[:20], 1):  # Show first 20 tasks
                file.write(f"{i}. {task['date']}: {task['task']} [{task['status']}]\n")
            
            if len(tasks) > 20:
                file.write(f"\n... and {len(tasks)-20} more tasks\n")
            
            file.write(f"\nEnd of Report - Generated on {datetime.now().strftime('%Y-%m-%d %H:%M')}")
        
        print(f"✅ Simple report created: {output_file}")
        print(f"📊 Processed {len(tasks)} tasks")
        print(f"✅ Completed: {completed} tasks")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    create_simple_report()
