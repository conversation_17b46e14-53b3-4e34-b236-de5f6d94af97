#!/usr/bin/env python3
"""
Convert DataTable Format Markdown to Professional Word Document
This script creates a clean, professional Word document similar to the sample format provided.
"""

import re
from docx import Document
from docx.shared import Inches, Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.oxml.shared import OxmlElement, qn

def add_table_borders(table):
    """Add professional borders to table"""
    for cell in table._cells:
        tc = cell._tc
        tcPr = tc.get_or_add_tcPr()
        tcBorders = OxmlElement('w:tcBorders')
        for border_name in ['top', 'left', 'bottom', 'right']:
            border = OxmlElement(f'w:{border_name}')
            border.set(qn('w:val'), 'single')
            border.set(qn('w:sz'), '6')
            border.set(qn('w:space'), '0')
            border.set(qn('w:color'), '000000')
            tcBorders.append(border)
        tcPr.append(tcBorders)

def convert_datatable_to_docx(markdown_file, output_file):
    """Convert datatable markdown to professional Word document"""
    
    # Read the markdown file
    with open(markdown_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Create a new Document
    doc = Document()
    
    # Set document margins
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(0.75)
        section.bottom_margin = Inches(0.75)
        section.left_margin = Inches(0.75)
        section.right_margin = Inches(0.75)
    
    # Add header
    header = sections[0].header
    header_para = header.paragraphs[0]
    header_para.text = "Jordan M Bunuan - Daily Time Record and Accomplishment Report"
    header_para.style.font.size = Pt(9)
    header_para.style.font.italic = True
    header_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Add custom styles
    styles = doc.styles
    
    # Title style
    title_style = styles.add_style('CustomTitle', WD_STYLE_TYPE.PARAGRAPH)
    title_style.font.name = 'Calibri'
    title_style.font.size = Pt(16)
    title_style.font.bold = True
    title_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
    title_style.paragraph_format.space_after = Pt(12)
    
    # Heading style
    h2_style = styles.add_style('CustomH2', WD_STYLE_TYPE.PARAGRAPH)
    h2_style.font.name = 'Calibri'
    h2_style.font.size = Pt(14)
    h2_style.font.bold = True
    h2_style.paragraph_format.space_before = Pt(12)
    h2_style.paragraph_format.space_after = Pt(6)
    
    # Body text style
    body_style = styles.add_style('CustomBody', WD_STYLE_TYPE.PARAGRAPH)
    body_style.font.name = 'Calibri'
    body_style.font.size = Pt(11)
    body_style.paragraph_format.space_after = Pt(3)
    
    # Split content into lines
    lines = content.split('\n')
    i = 0
    
    while i < len(lines):
        line = lines[i].strip()
        
        if not line:
            i += 1
            continue
            
        # Handle main title
        if line.startswith('# '):
            p = doc.add_paragraph(line[2:], style='CustomTitle')
            
        # Handle level 2 headings
        elif line.startswith('## '):
            p = doc.add_paragraph(line[3:], style='CustomH2')
            
        # Handle tables - Main daily task table
        elif line.startswith('|') and 'Tasks and Accomplishments' in line:
            # Parse the main daily tasks table
            table_lines = []
            while i < len(lines) and lines[i].strip().startswith('|'):
                table_lines.append(lines[i].strip())
                i += 1
            i -= 1  # Back up one since we'll increment at the end
            
            if len(table_lines) >= 3:  # Header + separator + data
                # Parse header
                header = [cell.strip() for cell in table_lines[0].split('|')[1:-1]]
                
                # Skip separator line
                data_lines = table_lines[2:]
                
                # Create table with specific column widths for daily tasks
                table = doc.add_table(rows=1, cols=3)
                table.alignment = WD_TABLE_ALIGNMENT.CENTER
                
                # Set column widths
                table.columns[0].width = Inches(0.8)  # Date column
                table.columns[1].width = Inches(4.5)  # Tasks column
                table.columns[2].width = Inches(0.7)  # Hours column
                
                # Add header with gray background
                hdr_cells = table.rows[0].cells
                for j, header_text in enumerate(header):
                    hdr_cells[j].text = header_text
                    # Format header cells
                    for paragraph in hdr_cells[j].paragraphs:
                        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                        for run in paragraph.runs:
                            run.bold = True
                            run.font.size = Pt(10)
                            run.font.name = 'Calibri'
                    
                    # Add gray background to header
                    shading_elm = OxmlElement('w:shd')
                    shading_elm.set(qn('w:fill'), 'D9D9D9')
                    hdr_cells[j]._tc.get_or_add_tcPr().append(shading_elm)
                
                # Add data rows
                for data_line in data_lines:
                    if data_line.strip() and data_line.startswith('|'):
                        cells_data = [cell.strip() for cell in data_line.split('|')[1:-1]]
                        if len(cells_data) == 3:
                            row_cells = table.add_row().cells
                            
                            # Date cell
                            row_cells[0].text = cells_data[0]
                            for paragraph in row_cells[0].paragraphs:
                                paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                                for run in paragraph.runs:
                                    run.font.size = Pt(9)
                                    run.font.name = 'Calibri'
                                    run.bold = True
                            
                            # Tasks cell - handle bullet points
                            tasks_text = cells_data[1].replace('<br>', '\n').replace('• ', '• ')
                            row_cells[1].text = tasks_text
                            for paragraph in row_cells[1].paragraphs:
                                paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
                                for run in paragraph.runs:
                                    run.font.size = Pt(9)
                                    run.font.name = 'Calibri'
                            
                            # Hours cell
                            row_cells[2].text = cells_data[2]
                            for paragraph in row_cells[2].paragraphs:
                                paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                                for run in paragraph.runs:
                                    run.font.size = Pt(9)
                                    run.font.name = 'Calibri'
                                    run.bold = True
                
                # Add borders
                add_table_borders(table)
                
                # Add spacing after table
                doc.add_paragraph()
        
        # Handle other tables (summary tables)
        elif line.startswith('|') and '|' in line:
            # Parse regular tables
            table_lines = []
            while i < len(lines) and lines[i].strip().startswith('|'):
                table_lines.append(lines[i].strip())
                i += 1
            i -= 1  # Back up one since we'll increment at the end
            
            if len(table_lines) >= 2:
                # Parse header
                header = [cell.strip() for cell in table_lines[0].split('|')[1:-1]]
                
                # Skip separator line
                data_lines = table_lines[2:] if len(table_lines) > 2 else []
                
                # Create table
                table = doc.add_table(rows=1, cols=len(header))
                table.alignment = WD_TABLE_ALIGNMENT.CENTER
                
                # Add header with gray background
                hdr_cells = table.rows[0].cells
                for j, header_text in enumerate(header):
                    hdr_cells[j].text = header_text
                    # Format header cells
                    for paragraph in hdr_cells[j].paragraphs:
                        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                        for run in paragraph.runs:
                            run.bold = True
                            run.font.size = Pt(10)
                            run.font.name = 'Calibri'
                    
                    # Add gray background to header
                    shading_elm = OxmlElement('w:shd')
                    shading_elm.set(qn('w:fill'), 'D9D9D9')
                    hdr_cells[j]._tc.get_or_add_tcPr().append(shading_elm)
                
                # Add data rows
                for data_line in data_lines:
                    if data_line.strip():
                        cells_data = [cell.strip() for cell in data_line.split('|')[1:-1]]
                        if len(cells_data) == len(header):
                            row_cells = table.add_row().cells
                            for j, cell_data in enumerate(cells_data):
                                row_cells[j].text = cell_data
                                # Format data cells
                                for paragraph in row_cells[j].paragraphs:
                                    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
                                    for run in paragraph.runs:
                                        run.font.size = Pt(9)
                                        run.font.name = 'Calibri'
                
                # Add borders
                add_table_borders(table)
                
                # Add spacing after table
                doc.add_paragraph()
        
        # Handle regular paragraphs
        else:
            if line and not line.startswith('---'):
                # Handle bold text
                if line.startswith('**') and line.endswith('**'):
                    p = doc.add_paragraph()
                    run = p.add_run(line[2:-2])
                    run.bold = True
                    run.font.name = 'Calibri'
                    run.font.size = Pt(11)
                else:
                    p = doc.add_paragraph(line, style='CustomBody')
        
        i += 1
    
    # Save the document
    doc.save(output_file)
    print(f"Professional datatable document saved as: {output_file}")

if __name__ == "__main__":
    markdown_file = "Jordan_Daily_Report_DataTable_Format.md"
    output_file = "Jordan_Daily_Report_DataTable_Format.docx"
    
    try:
        convert_datatable_to_docx(markdown_file, output_file)
        print("Conversion completed successfully!")
        print("The document now has a clean datatable format similar to your sample.")
    except Exception as e:
        print(f"Error during conversion: {e}")
